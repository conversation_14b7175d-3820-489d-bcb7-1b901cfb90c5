<template>
    <div class="brand-title">
        <img class="logo" :src="oemInfo?.loginLogo || `/src/assets/images/common/logo.png` " alt="logo" />
        <div class="branch-name">{{ oemInfo?.companyName || '臻企云·数字化产业融合协同平台' }}</div>
    </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'

const store = useStore<RootState>()

const oemInfo = computed(() => {
    const { oemConfig } = store.state.auth || {}
    const { modules } = oemConfig || {}
    const { config } = modules?.[0] || {}
    return config
})

onMounted(() => {
    console.log('BrandTitle mounted',oemInfo.value)
})
</script>

<style lang="scss" scoped>
.brand-title {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding-top: 100px;
    gap: 16px;
    z-index: 2;
}
.branch-name {
    font-size: var(--text-5xl);
    font-weight: var(--weight-500);
    color: var(--main-blue-);
}
.logo {
    width: 32px;
    height: 32px;
}

@media screen and (max-width: 1600px) {
    .branch-name {
        font-size: var(--text-4xl);
    }

    .logo {
        width: 28px;
        height: 28px;
    }

    .brand-title {
        gap: 14px;
    }
}

@media screen and (max-width: 1200px) {
    .branch-name {
        font-size: var(--text-2xl);
    }

    .logo {
        width: 22px;
        height: 22px;
    }

    .brand-title {
        gap: 12px;
    }
}

@media screen and (max-width: 992px) {
    .branch-name {
        font-size: var(--text-lg);
    }

    .logo {
        width: 16px;
        height: 16px;
    }

    .brand-title {
        gap: 10px;
    }
}

@media screen and (max-width: 768px) {
    .branch-name {
        font-size: var(--text-sm);
    }

    .logo {
        width: 12px;
        height: 12px;
    }

    .brand-title {
        gap: 8px;
    }
}

@media screen and (max-width: 576px) {
    .branch-name {
        font-size: var(--text-xs);
    }

    .logo {
        width: 10px;
        height: 10px;
    }

    .brand-title {
        gap: 6px;
    }
}
</style>
