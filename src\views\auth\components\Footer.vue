<template>
    <div class="footer">
        <div class="copyright">{{  oemInfo?.cDate || `2016-${currentYear}` }} {{ oemInfo?.cName || '数族科技（南京）股份有限公司'}}</div>
        <div class="version">{{ oemInfo?.tcp ? `备案号：${oemInfo.tcp}` : ` 版本号：${ version } ` }}</div>
    </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
import PKG from '@/../package.json'
const currentYear = new Date().getFullYear()
const version = PKG.version
const store = useStore<RootState>()

const oemInfo = computed(() => {
    const { oemConfig } = store.state.auth || {}
    const { modules } = oemConfig || {}
    const { config } = modules?.[0] || {}
    return config
})

onMounted(() => {
    console.log('Footer mounted', oemInfo.value)
})

</script>

<style scoped>
.footer {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding-bottom: 55px;
    z-index: 2;
    gap: 12px;
}

.footer div {
    font-size: var(--text-xl);
    color: var(--two-grey);
    font-weight: var(--weight-500);
}

@media screen and (max-width: 1600px) {
    .footer div {
        font-size: var(--text-base);
        line-height: var(--text-base);
    }
}

@media screen and (max-width: 1200px) {
    .footer div {
        font-size: var(--text-xs);
        line-height: var(--text-xs);
    }
}

@media screen and (max-width: 992px) {
    .footer div {
        font-size: 10px;
        line-height: 10px;
    }
}

@media screen and (max-width: 768px) {
    .footer div {
        font-size: 8px;
        line-height: 8px;
    }
}

@media screen and (max-width: 576px) {
    .footer div {
        font-size: 6px;
        line-height: 6px;
    }
}
</style>
