<script lang="tsx" setup>
import { useRouter } from 'vue-router'
import { Account, MessageBox, TaskManagement } from '../components/header'
import LOGO from '@/assets/images/header/logo.png'
import permissionService from '@/service/permissionService'
import { computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'

const store = useStore<RootState>()

const oemInfo = computed(() => {
    const { oemConfig } = store.state.auth || {}
    const { modules } = oemConfig || {}
    const { config } = modules?.[0] || {}
    return config
})

onMounted(() => {
    console.log('HeaderContainer mounted',oemInfo.value)
})

const router = useRouter()
const toRouter = () => {
    router.push('/')
}
</script>

<template>
    <div class="header display-flex top-bottom-center border-bottom">
        <div class="flex top-bottom-center width-100 h-48 lr-padding-24">
            <div class="flex pointer top-bottom-center" @click="toRouter">
                <img :src="oemInfo?.logoBk || LOGO" alt="" />
            </div>
            <div class="flex flex-1 justify-flex-end top-bottom-center gap-16 pointer">
                <TaskManagement v-if="permissionService.isShowTaskManagementMenu()"/>
                <!-- <AI /> -->
                <MessageBox />
                <!-- <Help /> -->
                <div class="w-1 back-color-border h-15"></div>
                <Account />
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped></style>
